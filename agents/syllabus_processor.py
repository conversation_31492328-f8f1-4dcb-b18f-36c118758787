# -*- coding: utf-8 -*-
"""
Service for processing syllabus text to JSON conversion.
"""

import logging
import uuid
from contextlib import contextmanager
# from typing import Optional, Dict, Any

from db_config.db import get_engine, CONTENT_SCHEMA
from db_config.pyqs_admin_db import get_exam_syllabus_text, update_exam_syllabus_json
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from agents.schemas.agent_prompts import syllabus_to_json_prompt

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class SyllabusProcessor:
    """
    Class for handling syllabus to JSON conversion operations.
    """

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(CONTENT_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error("Transaction rolled back due to error: " + str(e))
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def process_syllabus_to_json(self, exam_id, request_id=None):
        """
        Process syllabus text to JSON for a given exam ID.

        Args:
            exam_id (int): The exam ID to process
            request_id (str): Optional request ID for logging

        Returns:
            Dict: Result containing success status and data/error message
        """
        if not request_id:
            request_id = str(uuid.uuid4())

        try:
            logger.info("[REQUEST:" + request_id + "] Starting syllabus to JSON conversion for exam ID: " + str(exam_id))

            # Get syllabus text from database
            syllabus_text = get_exam_syllabus_text(exam_id)
            
            if not syllabus_text:
                logger.warning("[REQUEST:" + request_id + "] No syllabus text found for exam ID: " + str(exam_id))
                return {
                    "status": "error",
                    "message": "No syllabus text found for this exam"
                }

            if not syllabus_text.strip():
                logger.warning("[REQUEST:" + request_id + "] Empty syllabus text for exam ID: " + str(exam_id))
                return {
                    "status": "error",
                    "message": "Syllabus text is empty"
                }

            logger.info("[REQUEST:" + request_id + "] Retrieved syllabus text, length: " + str(len(syllabus_text)) + " characters")

            # Get LLM instance
            llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=300)

            # Create the prompt using syllabus_to_json_prompt
            prompt_text = syllabus_to_json_prompt(syllabus_text)

            logger.info("[REQUEST:" + request_id + "] Calling LLM for syllabus to JSON conversion")

            print(prompt_text)

            # Call LLM
            response = llm.invoke([HumanMessage(content=prompt_text)])

            print(response.content)

            # Extract content from response
            json_content = response.content

            logger.info("[REQUEST:" + request_id + "] LLM response received, length: " + str(len(json_content)) + " characters")

            # Update the database with the JSON content
            success = update_exam_syllabus_json(exam_id, json_content)
            
            if not success:
                logger.error("[REQUEST:" + request_id + "] Failed to update syllabus JSON in database")
                return {
                    "status": "error",
                    "message": "Failed to update syllabus JSON in database"
                }

            logger.info("[REQUEST:" + request_id + "] Successfully updated syllabus JSON for exam ID: " + str(exam_id))

            return {
                "status": "success",
                "message": "Syllabus successfully converted to JSON",
                "data": {
                    "exam_id": exam_id,
                    "json_content": json_content
                }
            }

        except Exception as e:
            logger.error("[REQUEST:" + request_id + "] Error processing syllabus to JSON: " + str(e))
            return {
                "status": "error",
                "message": "Error processing syllabus: " + str(e)
            }

    def get_syllabus_json(self, exam_id):
        """
        Get the syllabus JSON for a given exam ID.

        Args:
            exam_id (int): The exam ID

        Returns:
            Dict: Result containing success status and JSON data/error message
        """
        try:
            logger.info("Getting syllabus JSON for exam ID: " + str(exam_id))

            with self.get_connection() as cursor:
                query = f"""
                    SELECT syllabus_json
                    FROM {CONTENT_SCHEMA}.content_exam_mst
                    WHERE id = %s
                """

                cursor.execute(query, (exam_id,))
                row = cursor.fetchone()

                if not row:
                    logger.warning("No exam found with ID: " + str(exam_id))
                    return {
                        "status": "error",
                        "message": "Exam not found"
                    }

                syllabus_json = row[0]

                if not syllabus_json:
                    logger.warning("No syllabus JSON found for exam ID: " + str(exam_id))
                    return {
                        "status": "error",
                        "message": "No syllabus JSON found for this exam"
                    }

                return {
                    "status": "success",
                    "data": {
                        "exam_id": exam_id,
                        "syllabus_json": syllabus_json
                    }
                }

        except Exception as e:
            logger.error("Error getting syllabus JSON: " + str(e))
            return {
                "status": "error",
                "message": "Error retrieving syllabus JSON: " + str(e)
            }
