"""
Service for generating solutions for document questions using LLM.
"""

import json
import logging
import asyncio
import uuid
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

import config
from agents.syllabus_processor import SyllabusProcessor
from agents.schemas.agent_prompts import qp_solution_creator_prompt
from services.request_tracker_service import RequestTrackerService
from db_config.pyqs_admin_db import get_exam_questions_with_directions, get_exam_document_by_id, get_exam_by_id, update_exam_solution_fields
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from db_config.db import get_engine, CONTENT_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class DocumentSolutionGeneratorService:
    """
    Service for generating solutions for document questions using LLM.
    """

    def __init__(self, max_concurrent_generations=None):
        """
        Initialize the DocumentSolutionGeneratorService.
        """
        self.max_concurrent_generations = max_concurrent_generations or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        
        # Delay LLM initialization until first use
        self.llm = None
        self._llm_initialized = False
        
        # Initialize request tracker
        self.tracker_service = RequestTrackerService()

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(CONTENT_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error("Transaction rolled back due to error: " + str(e))
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def _initialize_llm(self):
        """Initialize LLM if not already initialized."""
        if not self._llm_initialized:
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True

    async def generate_solutions_for_document(self, document_id, username=None):
        """
        Generate solutions for all questions in a document.
        """
        # Create a task for tracking
        task_id = self.tracker_service.create_task("document_solution_generation")
        
        if not task_id:
            return {"status": "error", "message": "Failed to create tracking task"}

        # Start the solution generation process in background
        asyncio.create_task(self._generate_solutions_background(
            task_id, document_id, username
        ))
        
        return {"status": "success", "task_id": task_id}

    async def _generate_solutions_background(self, task_id, document_id, username=None):
        """
        Background task for generating solutions for document questions.
        """
        try:
            logger.info(f"[TASK:{task_id}] Starting solution generation for document ID {document_id}")
            
            # Update task status to in progress
            self.tracker_service.update_task_status(task_id, "IN_PROGRESS")
            
            # Step 1: Get document details
            logger.info(f"[TASK:{task_id}] Step 1: Getting document details")
            document = get_exam_document_by_id(document_id)
            if not document:
                raise Exception(f"Document with ID {document_id} not found")
            
            exam_id = document.get("content_exam_mst_id")
            if not exam_id:
                raise Exception(f"No exam ID found for document {document_id}")
            
            # Step 2: Get exam details
            logger.info(f"[TASK:{task_id}] Step 2: Getting exam details")
            exam = get_exam_by_id(exam_id)
            if not exam:
                raise Exception(f"Exam with ID {exam_id} not found")
            
            subject = exam.get("subject")
            if not subject:
                raise Exception(f"No subject found for exam {exam_id}")
            
            # Step 3: Get syllabus JSON
            logger.info(f"[TASK:{task_id}] Step 3: Getting syllabus JSON")
            processor = SyllabusProcessor()
            syllabus_result = processor.get_syllabus_json(exam_id)
            
            if syllabus_result["status"] != "success":
                raise Exception(f"Failed to get syllabus JSON: {syllabus_result['message']}")
            
            syllabus_json = syllabus_result["data"]["syllabus_json"]
            
            # Step 4: Get questions with directions
            logger.info(f"[TASK:{task_id}] Step 4: Getting questions with directions")
            questions = get_exam_questions_with_directions(document_id)
            
            if not questions:
                raise Exception(f"No questions found for document {document_id}")
            
            logger.info(f"[TASK:{task_id}] Found {len(questions)} questions to process")
            
            # Step 5: Generate solutions for each question and update database
            logger.info(f"[TASK:{task_id}] Step 5: Generating solutions and updating database")
            await self._generate_solutions_for_questions(
                task_id, questions, subject, syllabus_json
            )
            
            # Mark task as completed
            self.tracker_service.update_task_status(task_id, "COMPLETED")
            logger.info(f"[TASK:{task_id}] Solution generation completed successfully")
            
        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in solution generation: {e}")
            self.tracker_service.update_task_status(task_id, "FAILED", str(e))

    async def _generate_solutions_for_questions(self, task_id, questions, subject, syllabus_json):
        """Generate solutions for all questions using LLM and update database."""
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create semaphore for controlling concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent_generations)

            # Create tasks for parallel processing
            tasks = []
            for i, question in enumerate(questions):
                task = self._generate_single_solution(
                    semaphore, task_id, question, subject, syllabus_json, i + 1, len(questions)
                )
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for any failures
            failed_count = sum(1 for result in results if isinstance(result, Exception))
            success_count = len(results) - failed_count

            logger.info(f"[TASK:{task_id}] Solution generation completed: {success_count} successful, {failed_count} failed")

            if failed_count > 0:
                logger.warning(f"[TASK:{task_id}] {failed_count} solutions failed to generate")

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in solution generation: {e}")
            raise

    async def _generate_single_solution(self, semaphore, task_id, question, subject, syllabus_json, current_num, total_num):
        """Generate solution for a single question and update database."""
        async with semaphore:
            try:
                question_id = question.get("id")
                question_number = current_num
                
                logger.info(f"[TASK:{task_id}] Processing question {current_num}/{total_num} (ID: {question_id})")
                
                # Format question text
                formatted_question = self._format_question_text(question)
                
                # Create prompt using qp_solution_creator_prompt
                prompt_text = qp_solution_creator_prompt(subject, syllabus_json, formatted_question)
                
                # Call LLM
                response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])
                response_content = response.content.strip()

                # Parse JSON response
                solution_json = json.loads(response_content)

                # Extract fields from LLM response
                answer = solution_json.get("correctAnswerOption")
                topic = solution_json.get("topic")
                subtopic = solution_json.get("subtopic")
                difficulty_level = solution_json.get("difficultyLevel")
                solution_text = solution_json.get("solution")

                # Update database with solution fields
                success = update_exam_solution_fields(
                    solution_id=question_id,
                    answer=answer,
                    topic=topic,
                    subtopic=subtopic,
                    difficulty_level=difficulty_level,
                    solution=solution_text
                )

                if success:
                    logger.info(f"[TASK:{task_id}] Solution updated in database for question {current_num}/{total_num} (ID: {question_id})")
                else:
                    logger.warning(f"[TASK:{task_id}] Failed to update database for question {question_id}")

                return solution_json
                        
            except Exception as e:
                logger.error(f"[TASK:{task_id}] Failed to generate solution for question {question_id}: {e}")
                raise

    def _format_question_text(self, question):
        """Format question text according to the specified format."""
        formatted_parts = []
        
        # Add direction if present
        if question.get("direction") and question["direction"].get("directions"):
            formatted_parts.append(f"Direction - {question['direction']['directions']}")
        
        # Add question number and text
        question_text = question.get("question", "")
        formatted_parts.append(f"Question {question.get('id', '')}. {question_text}")
        
        # Add options
        for i in range(1, 6):  # Options 1-5
            option_key = f"option{i}"
            if question.get(option_key):
                formatted_parts.append(f"Option {i}")
                formatted_parts.append(question[option_key])
        
        return "\n".join(formatted_parts)
