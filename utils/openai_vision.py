from __future__ import annotations

import base64
import json
import logging
import time
from typing import List

from openai import OpenAI
import config
from app.content_extractor import ContentExtractor
from llm_manager.prompts import html_extractor_prompt_vision, question_extractor_prompt_vision, text_extraction_prompt
from config import llm_config
from llm_manager.llm_factory import LLMFactory
from utils.s3_utils import get_s3_path, read_file_from_s3
llm_factory = LLMFactory(llm_config)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize OpenAI client directly
client = OpenAI(api_key=config.OPENAI_API_KEY_ADMIN)


def encode_image(image_path):
    # Handle S3 paths
    if image_path.startswith('supload/'):
        # This is an S3 path, use S3 utilities to read it
        full_s3_path = get_s3_path(image_path)
        file_content = read_file_from_s3(full_s3_path)
        if file_content is None:
            raise FileNotFoundError(f"Failed to read S3 file: {image_path}")
        return base64.b64encode(file_content).decode("utf-8")
    else:
        # This is a local path, read it directly
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

def clean_llm_html(raw_html: str) -> str:
    """Clean HTML content returned by LLM

    Args:
        raw_html: Raw HTML content from LLM

    Returns:
        Cleaned HTML content
    """
    logger.info(f"Cleaning LLM HTML response, length: {len(raw_html)} characters")
    start_time = time.time()

    try:
        # Strip whitespace
        cleaned = raw_html.strip()

        # Remove markdown code blocks
        if cleaned.startswith("```html"):
            logger.info("Removing ```html markdown prefix")
            cleaned = cleaned.replace("```html", "").strip()
        if cleaned.startswith("```"):
            logger.info("Removing ``` markdown prefix")
            cleaned = cleaned.replace("```", "").strip()
        if cleaned.endswith("```"):
            logger.info("Removing ``` markdown suffix")
            cleaned = cleaned[:-3].strip()

        # Remove HTML, HEAD, BODY tags
        for tag in ["html", "head", "body"]:
            if f"<{tag}>" in cleaned or f"</{tag}>" in cleaned:
                logger.info(f"Removing {tag} tags")
                cleaned = cleaned.replace(f"<{tag}>", "")
                cleaned = cleaned.replace(f"</{tag}>", "")

        elapsed_time = time.time() - start_time
        logger.info(f"HTML cleaning completed in {elapsed_time:.2f} seconds, result length: {len(cleaned)} characters")
        return cleaned
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"HTML cleaning failed after {elapsed_time:.2f} seconds: {str(e)}")
        raise


def construct_input_content(prompt: str, image_paths: List[str]) -> list[
    dict[str, str] | dict[str, str | dict[str, str]]]:
    content = [
        {
            "type": "text",
            "text": prompt
        }
    ]

    # Add all images to content
    for path in image_paths:
        base64_image = encode_image(path)
        content.append({
            "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}",
                        }
        })
    return content


def parse_llm_result(response: str) -> str:
    """
    Parse LLM response into structured format

    Args:
        response: Raw LLM response

    Returns:
        Dict containing parsed questions
    """
    try:
        # Clean the response
        if response.startswith("```"):
            response = response[3:]
        elif response.startswith("```json") or response.startswith("```html"):
            response = response[7:]
        if response.endswith("```"):
            response = response[:-3]
        return response
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in LLM response: {str(e)}")
    except Exception as e:
        raise ValueError(f"Failed to parse LLM response: {str(e)}")


def extract_html_from_image(image_path: str, page_number: int = 1) -> str:
    """Extract HTML content from an image using OpenAI's Vision model

    Args:
        image_path: Path to the image file
        page_number: Page number for reference in the prompt

    Returns:
        str: Extracted HTML content
    """
    logger.info(f"Starting HTML extraction for page {page_number} from image: {image_path}")
    start_time = time.time()

    try:
        # Check if image file exists
        try:
            with open(image_path, "rb") as f:
                image_size = len(f.read())
            logger.info(f"Image file exists, size: {image_size} bytes")
        except Exception as e:
            logger.error(f"Error reading image file: {str(e)}")
            raise

        # Initialize extractor with direct OpenAI API
        logger.info("Initializing ContentExtractor with direct OpenAI API")
        try:
            extractor = ContentExtractor(llm="openai_admin", model="gpt-4.1-mini")
            logger.info("ContentExtractor initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing ContentExtractor: {str(e)}")
            raise

        # Prepare prompt and content
        logger.info("Preparing prompt and content for extraction")
        try:
            prompt = html_extractor_prompt_vision(str(page_number))
            # Log the prompt for debugging
            logger.info(f"Using HTML extractor prompt for page {page_number}:\n{prompt[:200]}...")
            content = construct_input_content(prompt, [image_path])
            logger.info(f"Prompt and content prepared, prompt length: {len(prompt)} characters")
        except Exception as e:
            logger.error(f"Error preparing prompt or content: {str(e)}")
            raise

        # Extract content from image
        logger.info("Extracting content from image using OpenAI API")
        try:
            final_validated_html = extractor.extract_content_from_images(
                content,
                parser_func=clean_llm_html
            )
            logger.info(f"Content extracted successfully, length: {len(str(final_validated_html))} characters")
        except Exception as e:
            logger.error(f"Error extracting content from image: {str(e)}")
            raise

        # Return the extracted content
        elapsed_time = time.time() - start_time
        logger.info(f"HTML extraction completed in {elapsed_time:.2f} seconds")

        if isinstance(final_validated_html, str):
            return final_validated_html
        else:
            return str(final_validated_html)

    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"HTML extraction failed after {elapsed_time:.2f} seconds: {str(e)}")
        raise


def extract_text_from_image(image_path: str, page_number: int = 1) -> str:
    """Extract plain text from an image using OpenAI's Vision model

    Args:
        image_path: Path to the image file
        page_number: Page number for reference in the prompt

    Returns:
        str: Extracted text content
    """
    # Use ContentExtractor with direct OpenAI API
    extractor = ContentExtractor(llm="openai_admin", model="gpt-4.1-mini")

    prompt = question_extractor_prompt_vision(str(page_number))
    # Log the prompt for debugging
    logger.info(f"Using question extractor prompt for page {page_number}:\n{prompt[:200]}...")

    content = construct_input_content(prompt, [image_path])

    extracted_text = extractor.extract_content_from_images(
        content,
        parser_func=parse_llm_result
    )

    if isinstance(extracted_text, str):
        return extracted_text
    else:
        return str(extracted_text)
