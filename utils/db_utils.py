"""
Database utility functions for direct database operations.
"""

import logging
from db_config.db import get_engine

logger = logging.getLogger(__name__)

def update_extract_path(res_id, extract_path, schema="wscontent"):
    """
    Update the extract_path in the resource_dtl table using direct connection.

    Args:
        res_id: Resource ID
        extract_path: Path to the extracted text
        schema: Database schema (default: wscontent)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the engine
        engine = get_engine(schema)

        # Create a new connection
        connection = engine.raw_connection()

        try:
            # Create a cursor
            cursor = connection.cursor()

            # Prepare the update query
            update_query = f"""
                UPDATE {schema}.resource_dtl
                SET extract_path = %s
                WHERE id = %s
            """

            # Execute the update query
            cursor.execute(update_query, (extract_path, res_id))

            # Log the result of the update
            rows_affected = cursor.rowcount
            logger.info(f"Database update result: {rows_affected} rows affected")

            # Verify the update was successful
            verify_query = f"""
                SELECT extract_path
                FROM {schema}.resource_dtl
                WHERE id = %s
            """

            cursor.execute(verify_query, (res_id,))
            verify_row = cursor.fetchone()

            if verify_row and verify_row[0] == extract_path:
                logger.info(f"Verified database update: extract_path is now {verify_row[0]}")
                success = True
            else:
                stored_path = verify_row[0] if verify_row else "None"
                logger.warning(f"Database update verification failed: expected {extract_path}, got {stored_path}")
                success = False

            # Commit the transaction
            connection.commit()
            logger.info("Database transaction committed successfully")

            return success

        except Exception as e:
            # Rollback the transaction
            connection.rollback()
            logger.error(f"Error updating database: {e}")
            return False
        finally:
            # Close the connection
            connection.close()

    except Exception as e:
        logger.error(f"Error getting database connection: {e}")
        return False


def update_vector_stored(res_id, original_namespace, schema="wscontent"):
    """
    Update the vector_stored field in the resource_dtl table using direct connection.

    Args:
        res_id: Resource ID
        original_namespace: Original namespace (without splitting)
        schema: Database schema (default: wscontent)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the engine
        engine = get_engine(schema)

        # Create a new connection
        connection = engine.raw_connection()

        try:
            # Create a cursor
            cursor = connection.cursor()

            # Prepare the update query
            update_query = f"""
                UPDATE {schema}.resource_dtl
                SET vector_stored = %s
                WHERE id = %s
            """

            # Execute the update query
            cursor.execute(update_query, (original_namespace, res_id))

            # Log the result of the update
            rows_affected = cursor.rowcount
            logger.info(f"Vector stored update result: {rows_affected} rows affected")

            # Verify the update was successful
            verify_query = f"""
                SELECT vector_stored
                FROM {schema}.resource_dtl
                WHERE id = %s
            """

            cursor.execute(verify_query, (res_id,))
            verify_row = cursor.fetchone()

            if verify_row and verify_row[0] == original_namespace:
                logger.info(f"Verified vector_stored update: vector_stored is now {verify_row[0]}")
                success = True
            else:
                stored_namespace = verify_row[0] if verify_row else "None"
                logger.warning(f"Vector stored update verification failed: expected {original_namespace}, got {stored_namespace}")
                success = False

            # Commit the transaction
            connection.commit()
            logger.info("Vector stored database transaction committed successfully")

            return success

        except Exception as e:
            # Rollback the transaction
            connection.rollback()
            logger.error(f"Error updating vector_stored in database: {e}")
            return False
        finally:
            # Close the connection
            connection.close()

    except Exception as e:
        logger.error(f"Error getting database connection for vector_stored update: {e}")
        return False
